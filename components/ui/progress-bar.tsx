"use client"

import * as React from "react"
import { cn } from "@/lib/utils"

interface ProgressBarProps extends React.HTMLAttributes<HTMLDivElement> {
  value: number
  max?: number
  variant?: "default" | "electric" | "gradient"
  size?: "sm" | "md" | "lg"
  showValue?: boolean
  animated?: boolean
}

const ProgressBar = React.forwardRef<HTMLDivElement, ProgressBarProps>(
  ({ 
    className, 
    value, 
    max = 100, 
    variant = "default", 
    size = "md", 
    showValue = false,
    animated = true,
    ...props 
  }, ref) => {
    const percentage = Math.min(Math.max((value / max) * 100, 0), 100)

    const sizeClasses = {
      sm: "h-1",
      md: "h-2",
      lg: "h-3",
    }

    const variantClasses = {
      default: "bg-electric-blue",
      electric: "bg-gradient-electric",
      gradient: "bg-gradient-to-r from-electric-blue via-blue-400 to-electric-blue",
    }

    return (
      <div
        ref={ref}
        className={cn("w-full bg-secondary rounded-full overflow-hidden", sizeClasses[size], className)}
        {...props}
      >
        <div
          className={cn(
            "h-full rounded-full transition-all duration-500 ease-out",
            variantClasses[variant],
            animated && "animate-pulse"
          )}
          style={{ width: `${percentage}%` }}
        />
        {showValue && (
          <div className="mt-2 text-center">
            <span className="text-sm font-medium text-foreground">
              {Math.round(percentage)}%
            </span>
          </div>
        )}
      </div>
    )
  }
)
ProgressBar.displayName = "ProgressBar"

export { ProgressBar }

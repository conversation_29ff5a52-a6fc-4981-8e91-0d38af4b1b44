import * as React from "react";
import { cn } from "@/lib/utils";
import { Card } from "./card";

interface StatCardProps extends React.HTMLAttributes<HTMLDivElement> {
  title: string;
  value: string | number | React.ReactNode;
  change?: string;
  changeType?: "positive" | "negative" | "neutral";
  icon?: React.ReactNode;
  variant?: "default" | "glass" | "electric";
}

const StatCard = React.forwardRef<HTMLDivElement, StatCardProps>(
  (
    {
      className,
      title,
      value,
      change,
      changeType = "neutral",
      icon,
      variant = "default",
      ...props
    },
    ref
  ) => {
    const changeColors = {
      positive: "text-green-400",
      negative: "text-red-400",
      neutral: "text-gray-400",
    };

    return (
      <Card
        ref={ref}
        variant={variant}
        className={cn("p-6 hover:scale-105 transition-transform duration-300", className)}
        {...props}
      >
        <div className="flex items-start justify-between">
          <div className="space-y-2">
            <p className="text-sm font-medium text-muted-foreground">{title}</p>
            <div className="flex items-baseline space-x-2">
              <div className="text-3xl font-space-grotesk font-bold text-foreground">{value}</div>
              {change && (
                <span className={cn("text-sm font-medium", changeColors[changeType])}>
                  {change}
                </span>
              )}
            </div>
          </div>
          {icon && <div className="text-electric-blue opacity-80">{icon}</div>}
        </div>
      </Card>
    );
  }
);
StatCard.displayName = "StatCard";

export { StatCard };

"use client"

import * as React from "react"
import { cn } from "@/lib/utils"

interface ParallaxSectionProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
  speed?: number
  direction?: "up" | "down"
}

const ParallaxSection = React.forwardRef<HTMLDivElement, ParallaxSectionProps>(
  ({ className, children, speed = 0.5, direction = "up", ...props }, ref) => {
    const [offset, setOffset] = React.useState(0)
    const elementRef = React.useRef<HTMLDivElement>(null)

    React.useEffect(() => {
      const handleScroll = () => {
        if (!elementRef.current) return

        const rect = elementRef.current.getBoundingClientRect()
        const scrolled = window.pageYOffset
        const rate = scrolled * -speed

        if (rect.bottom >= 0 && rect.top <= window.innerHeight) {
          setOffset(direction === "up" ? rate : -rate)
        }
      }

      window.addEventListener("scroll", handleScroll)
      return () => window.removeEventListener("scroll", handleScroll)
    }, [speed, direction])

    return (
      <div
        ref={(node) => {
          elementRef.current = node
          if (typeof ref === "function") {
            ref(node)
          } else if (ref) {
            ref.current = node
          }
        }}
        className={cn("transition-transform duration-75 ease-out", className)}
        style={{
          transform: `translateY(${offset}px)`,
        }}
        {...props}
      >
        {children}
      </div>
    )
  }
)
ParallaxSection.displayName = "ParallaxSection"

export { ParallaxSection }

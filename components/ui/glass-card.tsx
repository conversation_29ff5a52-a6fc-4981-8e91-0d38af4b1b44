import * as React from "react"
import { cn } from "@/lib/utils"

interface GlassCardProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
  blur?: "sm" | "md" | "lg" | "xl"
  opacity?: "low" | "medium" | "high"
}

const GlassCard = React.forwardRef<HTMLDivElement, GlassCardProps>(
  ({ className, children, blur = "md", opacity = "medium", ...props }, ref) => {
    const blurClasses = {
      sm: "backdrop-blur-sm",
      md: "backdrop-blur-md", 
      lg: "backdrop-blur-lg",
      xl: "backdrop-blur-xl"
    }

    const opacityClasses = {
      low: "bg-white/5 border-white/10",
      medium: "bg-white/10 border-white/20", 
      high: "bg-white/20 border-white/30"
    }

    return (
      <div
        ref={ref}
        className={cn(
          "rounded-2xl border shadow-xl transition-all duration-300 hover:shadow-2xl",
          blurClasses[blur],
          opacityClasses[opacity],
          className
        )}
        {...props}
      >
        {children}
      </div>
    )
  }
)
GlassCard.displayName = "GlassCard"

export { GlassCard }

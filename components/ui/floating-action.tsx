"use client"

import * as React from "react"
import { cn } from "@/lib/utils"

interface FloatingActionProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children: React.ReactNode
  position?: "bottom-right" | "bottom-left" | "top-right" | "top-left"
  variant?: "default" | "electric" | "glass"
  size?: "sm" | "md" | "lg"
}

const FloatingAction = React.forwardRef<HTMLButtonElement, FloatingActionProps>(
  ({ 
    className, 
    children, 
    position = "bottom-right", 
    variant = "electric", 
    size = "md",
    ...props 
  }, ref) => {
    const positionClasses = {
      "bottom-right": "fixed bottom-6 right-6",
      "bottom-left": "fixed bottom-6 left-6",
      "top-right": "fixed top-6 right-6",
      "top-left": "fixed top-6 left-6",
    }

    const sizeClasses = {
      sm: "w-12 h-12",
      md: "w-14 h-14",
      lg: "w-16 h-16",
    }

    const variantClasses = {
      default: "bg-secondary text-foreground hover:bg-secondary/80",
      electric: "bg-gradient-electric text-white hover:shadow-electric-blue/40 hover:shadow-xl",
      glass: "bg-white/10 backdrop-blur-md border border-white/20 text-white hover:bg-white/20",
    }

    return (
      <button
        ref={ref}
        className={cn(
          "z-50 rounded-full shadow-lg transition-all duration-300 ease-out",
          "hover:scale-110 active:scale-95",
          "flex items-center justify-center",
          positionClasses[position],
          sizeClasses[size],
          variantClasses[variant],
          className
        )}
        {...props}
      >
        {children}
      </button>
    )
  }
)
FloatingAction.displayName = "FloatingAction"

export { FloatingAction }

import * as React from "react"
import { cn } from "@/lib/utils"

interface GradientTextProps extends React.HTMLAttributes<HTMLSpanElement> {
  children: React.ReactNode
  variant?: "electric" | "blue-white" | "custom"
  from?: string
  to?: string
}

const GradientText = React.forwardRef<HTMLSpanElement, GradientTextProps>(
  ({ className, children, variant = "electric", from, to, ...props }, ref) => {
    const gradientClasses = {
      electric: "bg-gradient-to-r from-electric-blue to-blue-400 bg-clip-text text-transparent",
      "blue-white": "bg-gradient-to-r from-electric-blue to-white bg-clip-text text-transparent",
      custom: from && to ? `bg-gradient-to-r from-[${from}] to-[${to}] bg-clip-text text-transparent` : ""
    }

    return (
      <span
        ref={ref}
        className={cn(
          "font-semibold",
          variant === "custom" && from && to 
            ? `bg-gradient-to-r from-[${from}] to-[${to}] bg-clip-text text-transparent`
            : gradientClasses[variant],
          className
        )}
        {...props}
      >
        {children}
      </span>
    )
  }
)
GradientText.displayName = "GradientText"

export { GradientText }

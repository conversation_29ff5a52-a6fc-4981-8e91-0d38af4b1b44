"use client"

import * as React from "react"
import { cn } from "@/lib/utils"

interface HoverCardProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
  hoverEffect?: "lift" | "glow" | "scale" | "tilt" | "electric"
  intensity?: "subtle" | "medium" | "strong"
}

const HoverCard = React.forwardRef<HTMLDivElement, HoverCardProps>(
  ({ className, children, hoverEffect = "lift", intensity = "medium", ...props }, ref) => {
    const effectClasses = {
      lift: {
        subtle: "hover:translate-y-[-2px] hover:shadow-lg",
        medium: "hover:translate-y-[-4px] hover:shadow-xl",
        strong: "hover:translate-y-[-8px] hover:shadow-2xl",
      },
      glow: {
        subtle: "hover:shadow-electric-blue/20 hover:shadow-lg",
        medium: "hover:shadow-electric-blue/30 hover:shadow-xl",
        strong: "hover:shadow-electric-blue/40 hover:shadow-2xl",
      },
      scale: {
        subtle: "hover:scale-[1.02]",
        medium: "hover:scale-105",
        strong: "hover:scale-110",
      },
      tilt: {
        subtle: "hover:rotate-1",
        medium: "hover:rotate-2",
        strong: "hover:rotate-3",
      },
      electric: {
        subtle: "hover:border-electric-blue/30 hover:shadow-electric-blue/20 hover:shadow-lg",
        medium: "hover:border-electric-blue/50 hover:shadow-electric-blue/30 hover:shadow-xl",
        strong: "hover:border-electric-blue/70 hover:shadow-electric-blue/40 hover:shadow-2xl hover:scale-105",
      },
    }

    return (
      <div
        ref={ref}
        className={cn(
          "transition-all duration-300 ease-out cursor-pointer",
          effectClasses[hoverEffect][intensity],
          className
        )}
        {...props}
      >
        {children}
      </div>
    )
  }
)
HoverCard.displayName = "HoverCard"

export { HoverCard }

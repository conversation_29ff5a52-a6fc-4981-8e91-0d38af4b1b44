"use client"

import * as React from "react"
import { cn } from "@/lib/utils"

interface AnimatedCounterProps extends React.HTMLAttributes<HTMLSpanElement> {
  value: number
  duration?: number
  prefix?: string
  suffix?: string
  decimals?: number
  separator?: string
}

const AnimatedCounter = React.forwardRef<HTMLSpanElement, AnimatedCounterProps>(
  ({ 
    className, 
    value, 
    duration = 2000, 
    prefix = "", 
    suffix = "", 
    decimals = 0,
    separator = ",",
    ...props 
  }, ref) => {
    const [count, setCount] = React.useState(0)
    const [isVisible, setIsVisible] = React.useState(false)
    const elementRef = React.useRef<HTMLSpanElement>(null)

    React.useEffect(() => {
      const observer = new IntersectionObserver(
        ([entry]) => {
          if (entry.isIntersecting) {
            setIsVisible(true)
          }
        },
        { threshold: 0.1 }
      )

      if (elementRef.current) {
        observer.observe(elementRef.current)
      }

      return () => observer.disconnect()
    }, [])

    React.useEffect(() => {
      if (!isVisible) return

      let startTime: number
      let animationFrame: number

      const animate = (timestamp: number) => {
        if (!startTime) startTime = timestamp
        const progress = Math.min((timestamp - startTime) / duration, 1)
        
        // Easing function for smooth animation
        const easeOutQuart = 1 - Math.pow(1 - progress, 4)
        setCount(value * easeOutQuart)

        if (progress < 1) {
          animationFrame = requestAnimationFrame(animate)
        }
      }

      animationFrame = requestAnimationFrame(animate)

      return () => {
        if (animationFrame) {
          cancelAnimationFrame(animationFrame)
        }
      }
    }, [isVisible, value, duration])

    const formatNumber = (num: number) => {
      const fixed = num.toFixed(decimals)
      const parts = fixed.split('.')
      parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, separator)
      return parts.join('.')
    }

    return (
      <span
        ref={(node) => {
          elementRef.current = node
          if (typeof ref === 'function') {
            ref(node)
          } else if (ref) {
            ref.current = node
          }
        }}
        className={cn("font-space-grotesk font-bold tabular-nums", className)}
        {...props}
      >
        {prefix}{formatNumber(count)}{suffix}
      </span>
    )
  }
)
AnimatedCounter.displayName = "AnimatedCounter"

export { AnimatedCounter }

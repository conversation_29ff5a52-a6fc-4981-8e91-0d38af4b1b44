import { Link, TrendingDown, TrendingUp, Users } from "lucide-react";
import { StatCard } from "@/components/ui/stat-card";
import { AnimatedCounter } from "@/components/ui/animated-counter";
import { GradientText } from "@/components/ui/gradient-text";

const PerformanceStats = () => {
  const stats = [
    {
      icon: TrendingUp,
      title: "Average Monthly ROI",
      value: 21.5,
      suffix: "%",
      prefix: "+",
      change: "+3.2% vs last month",
      changeType: "positive" as const,
    },
    {
      icon: Users,
      title: "Active Users",
      value: 5200,
      suffix: "+",
      change: "+12% this quarter",
      changeType: "positive" as const,
    },
    {
      icon: Link,
      title: "Exchanges Supported",
      value: 3,
      description: "Binance, Bybit, OKX",
      change: "Enterprise grade",
      changeType: "neutral" as const,
    },
    {
      icon: TrendingDown,
      title: "Average Drawdown",
      value: 32,
      suffix: "%",
      prefix: "-",
      change: "vs market -47%",
      changeType: "positive" as const,
    },
  ];

  return (
    <section className="py-20 bg-background">
      <div className="max-w-7xl mx-auto px-6">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-space-grotesk font-bold text-foreground mb-4 animate-fade-in">
            📊 Our Numbers Speak for{" "}
            <GradientText variant="electric">Themselves</GradientText>
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Real performance metrics from our AI-powered trading platform
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {stats.map((stat, index) => {
            const Icon = stat.icon;
            return (
              <StatCard
                key={index}
                title={stat.title}
                value={
                  <AnimatedCounter
                    value={stat.value}
                    prefix={stat.prefix}
                    suffix={stat.suffix}
                    duration={2000 + index * 200}
                  />
                }
                change={stat.change}
                changeType={stat.changeType}
                icon={<Icon className="w-6 h-6" />}
                variant="glass"
                className="animate-scale-in"
                style={{ animationDelay: `${index * 150}ms` }}
              />
            );
          })}
        </div>

        {/* Additional Context */}
        <div className="mt-16 text-center">
          <div className="inline-flex items-center gap-4 bg-secondary/50 backdrop-blur-sm border border-border rounded-2xl px-8 py-4">
            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
            <span className="text-sm text-muted-foreground">
              Data updated in real-time • Last update: {new Date().toLocaleTimeString()}
            </span>
          </div>
        </div>
      </div>
    </section>
  );
};

export default PerformanceStats;

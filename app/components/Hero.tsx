import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>p,
  Zap,
  Lock,
  CreditCard,
} from "lucide-react";
import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { GradientText } from "@/components/ui/gradient-text";
import { GlassCard } from "@/components/ui/glass-card";

const Hero = () => {
  return (
    <section className="relative min-h-screen bg-background overflow-hidden">
      {/* Sophisticated Background */}
      <div className="absolute inset-0">
        {/* Gradient Background */}
        <div className="absolute inset-0 bg-gradient-lightquant opacity-60" />
        
        {/* Grid <PERSON>tern */}
        <div 
          className="absolute inset-0 opacity-20"
          style={{
            backgroundImage: `
              linear-gradient(90deg, rgba(34, 139, 255, 0.1) 1px, transparent 1px),
              linear-gradient(rgba(34, 139, 255, 0.1) 1px, transparent 1px)
            `,
            backgroundSize: "50px 50px",
          }}
        />

        {/* Floating Elements */}
        <div className="absolute top-20 left-20 w-32 h-32 bg-electric-blue/10 rounded-full blur-xl animate-float opacity-60" />
        <div className="absolute top-40 right-32 w-24 h-24 bg-electric-blue/20 rounded-full blur-xl animate-float delay-1000 opacity-50" />
        <div className="absolute bottom-32 left-40 w-40 h-40 bg-electric-blue/5 rounded-full blur-2xl animate-float delay-2000 opacity-40" />
      </div>

      {/* Main Content */}
      <div className="relative z-10 max-w-7xl mx-auto px-6 pt-32 pb-16">
        <div className="text-center">
          {/* Status Badge */}
          <div className="mb-8 animate-fade-in">
            <Badge variant="glass" className="px-6 py-3 text-sm font-medium">
              <Zap className="w-4 h-4 mr-2 text-electric-blue" />
              AI-POWERED TRADING REVOLUTION
              <div className="ml-2 flex gap-1">
                <div className="w-1.5 h-1.5 bg-green-400 rounded-full animate-pulse" />
                <div className="w-1.5 h-1.5 bg-electric-blue rounded-full animate-pulse delay-200" />
                <div className="w-1.5 h-1.5 bg-purple-400 rounded-full animate-pulse delay-400" />
              </div>
            </Badge>
          </div>

          {/* Main Headline */}
          <div className="mb-8">
            <h1 className="text-6xl md:text-8xl font-space-grotesk font-black text-foreground mb-4 leading-none animate-slide-up tracking-tight">
              <span className="block">AI-POWERED</span>
              <span className="block">
                <GradientText variant="electric" className="text-6xl md:text-8xl font-black">
                  CRYPTO TRADING
                </GradientText>
              </span>
              <span className="block">BOTS</span>
            </h1>
          </div>

          {/* Tagline */}
          <div className="mb-8 animate-slide-up delay-300">
            <p className="text-2xl md:text-3xl text-muted-foreground mb-4 font-light tracking-wide">
              Smarter. Faster. Fully Automated.
            </p>
            <div className="flex items-center justify-center gap-4 text-muted-foreground">
              <div className="w-16 h-px bg-gradient-to-r from-transparent to-border" />
              <span className="text-sm font-medium tracking-widest uppercase">
                <GradientText variant="electric">Premium AI Trading</GradientText>
              </span>
              <div className="w-16 h-px bg-gradient-to-l from-transparent to-border" />
            </div>
          </div>

          {/* Description */}
          <p className="text-xl text-muted-foreground mb-12 max-w-4xl mx-auto leading-relaxed animate-slide-up delay-500 font-light">
            Discover intelligent crypto trading bots trained on real market data.{" "}
            <span className="text-foreground font-medium">
              Optimize profits, reduce risk, and automate your strategy
            </span>{" "}
            — all in one click.
          </p>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-6 justify-center mb-16 animate-slide-up delay-700">
            <Button
              variant="electric"
              size="xl"
              className="group"
            >
              <Play className="w-5 h-5 mr-2 group-hover:scale-110 transition-transform" />
              Start Free Trial – 7 Days
              <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
            </Button>

            <Button
              variant="outline"
              size="xl"
              className="group"
            >
              <BarChart3 className="w-5 h-5 mr-2 group-hover:scale-110 transition-transform" />
              View Bot Performance
            </Button>
          </div>

          {/* Feature Cards */}
          <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto mb-16">
            {[
              {
                icon: TrendingUp,
                title: "AI-Driven Intelligence",
                description: "Personalized bots adapted to your risk profile",
                delay: "delay-800",
              },
              {
                icon: Shield,
                title: "Enterprise Security",
                description: "Bank-grade API integration with major exchanges",
                delay: "delay-900",
              },
              {
                icon: BarChart3,
                title: "Real-Time Analytics",
                description: "Transparent performance with live insights",
                delay: "delay-1000",
              },
            ].map((feature, index) => {
              const Icon = feature.icon;
              return (
                <GlassCard
                  key={index}
                  className={`p-8 hover:scale-105 transition-all duration-300 animate-scale-in ${feature.delay} cursor-pointer group`}
                >
                  <div className="text-center">
                    <div className="mb-6">
                      <Icon className="w-12 h-12 text-electric-blue mx-auto group-hover:scale-110 transition-transform duration-300" />
                    </div>
                    <h3 className="text-lg font-space-grotesk font-bold text-foreground mb-3">
                      {feature.title}
                    </h3>
                    <p className="text-muted-foreground leading-relaxed">
                      {feature.description}
                    </p>
                  </div>
                </GlassCard>
              );
            })}
          </div>

          {/* Trust Indicators */}
          <div className="animate-fade-in delay-1100">
            <div className="flex flex-wrap justify-center items-center gap-6">
              {[
                { icon: Lock, text: "SSL Encrypted", color: "bg-green-400" },
                { icon: Zap, text: "7-Day Free Trial", color: "bg-electric-blue" },
                { icon: CreditCard, text: "No Credit Card Required", color: "bg-purple-400" },
              ].map((item, index) => (
                <Badge
                  key={index}
                  variant="glass"
                  className="px-4 py-2 hover:scale-105 transition-transform cursor-pointer"
                >
                  <div className={`w-2 h-2 ${item.color} rounded-full mr-2 animate-pulse`} />
                  <item.icon className="w-4 h-4 mr-1" />
                  {item.text}
                </Badge>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Gradient */}
      <div className="absolute bottom-0 left-0 right-0 h-40 bg-gradient-to-t from-background via-background/50 to-transparent" />
    </section>
  );
};

export default Hero;

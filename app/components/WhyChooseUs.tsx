import { Bar<PERSON>hart3, Brain, Network, Shield, Zap } from "lucide-react";
import { GradientText } from "@/components/ui/gradient-text";
import { GlassCard } from "@/components/ui/glass-card";

const WhyChooseUs = () => {
  const features = [
    {
      icon: Brain,
      title: "AI Optimization",
      description: "Constantly improves strategy with market feedback",
    },
    {
      icon: Shield,
      title: "Risk Engine",
      description: "Adaptive capital allocation & stop-loss",
    },
    {
      icon: BarChart3,
      title: "Backtest Transparency",
      description: "All bots show full historical metrics",
    },
    {
      icon: Zap,
      title: "Auto Deployment",
      description: "One-click to follow, no code required",
    },
    {
      icon: Network,
      title: "Multi-exchange Support",
      description: "Binance, Bybit, OKX, and more coming",
    },
  ];

  return (
    <section className="py-20 bg-gradient-lightquant">
      <div className="max-w-7xl mx-auto px-6">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-space-grotesk font-bold text-foreground mb-4 animate-fade-in">
            🚀 Why Choose <GradientText variant="electric">LightQuant</GradientText>?
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto animate-fade-in delay-200">
            We've built the most advanced AI trading platform with features that matter to serious
            traders.
          </p>
        </div>

        {/* Features Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          {features.map((feature, index) => {
            const Icon = feature.icon;
            return (
              <GlassCard
                key={index}
                className="p-8 hover:scale-105 transition-all duration-300 animate-scale-in group cursor-pointer"
                style={{ animationDelay: `${index * 150}ms` }}
              >
                <div className="text-center">
                  <div className="mb-6">
                    <div className="w-16 h-16 bg-electric-blue/10 border border-electric-blue/20 rounded-2xl flex items-center justify-center mx-auto group-hover:scale-110 transition-transform duration-300">
                      <Icon className="w-8 h-8 text-electric-blue" />
                    </div>
                  </div>
                  
                  <h3 className="text-xl font-space-grotesk font-bold text-foreground mb-3">
                    {feature.title}
                  </h3>
                  
                  <p className="text-muted-foreground leading-relaxed">
                    {feature.description}
                  </p>
                </div>
              </GlassCard>
            );
          })}
        </div>

        {/* Bottom CTA */}
        <div className="text-center">
          <div className="inline-flex items-center gap-4 bg-secondary/50 backdrop-blur-sm border border-border rounded-2xl px-8 py-4">
            <div className="w-2 h-2 bg-electric-blue rounded-full animate-pulse" />
            <span className="text-sm text-muted-foreground">
              Trusted by 5,200+ traders worldwide
            </span>
          </div>
        </div>
      </div>
    </section>
  );
};

export default WhyChooseUs;

import { Mail, MessageCircle, Send, Twitter } from "lucide-react";
import Link from "next/link";
import { GradientText } from "@/components/ui/gradient-text";
import { Separator } from "@/components/ui/separator";

const Footer = () => {
  return (
    <footer className="bg-gradient-lightquant border-t border-border py-16">
      <div className="max-w-7xl mx-auto px-6">
        <div className="grid md:grid-cols-4 gap-8 mb-12">
          {/* Brand */}
          <div className="md:col-span-1">
            <h3 className="text-2xl font-space-grotesk font-bold mb-4">
              Light<GradientText variant="electric">Quant</GradientText>
            </h3>
            <p className="text-muted-foreground leading-relaxed">
              A fully automated, smart trading platform that eliminates emotional bias and gives
              everyday users access to AI-driven quant tools.
            </p>
          </div>

          {/* Links */}
          <div className="md:col-span-2 grid md:grid-cols-2 gap-8">
            <div>
              <h4 className="font-space-grotesk font-semibold mb-4 text-electric-blue">Platform</h4>
              <ul className="space-y-3 text-muted-foreground">
                <li>
                  <Link
                    href="/about"
                    className="hover:text-foreground transition-colors duration-300"
                  >
                    About Us
                  </Link>
                </li>
                <li>
                  <Link
                    href="/blog"
                    className="hover:text-foreground transition-colors duration-300"
                  >
                    Blog
                  </Link>
                </li>
                <li>
                  <Link
                    href="/features"
                    className="hover:text-foreground transition-colors duration-300"
                  >
                    Features
                  </Link>
                </li>
                <li>
                  <Link
                    href="/pricing"
                    className="hover:text-foreground transition-colors duration-300"
                  >
                    Pricing
                  </Link>
                </li>
              </ul>
            </div>
            <div>
              <h4 className="font-space-grotesk font-semibold mb-4 text-electric-blue">Legal</h4>
              <ul className="space-y-3 text-muted-foreground">
                <li>
                  <Link
                    href="/terms"
                    className="hover:text-foreground transition-colors duration-300"
                  >
                    Terms of Service
                  </Link>
                </li>
                <li>
                  <Link
                    href="/privacy"
                    className="hover:text-foreground transition-colors duration-300"
                  >
                    Privacy Policy
                  </Link>
                </li>
                <li>
                  <Link
                    href="/risk"
                    className="hover:text-foreground transition-colors duration-300"
                  >
                    Risk Disclosure
                  </Link>
                </li>
                <li>
                  <Link
                    href="/docs"
                    className="hover:text-foreground transition-colors duration-300"
                  >
                    API Documentation
                  </Link>
                </li>
              </ul>
            </div>
          </div>

          {/* Contact & Social */}
          <div className="md:col-span-1">
            <h4 className="font-space-grotesk font-semibold mb-4 text-electric-blue">Connect</h4>
            <div className="space-y-4">
              <a
                href="mailto:<EMAIL>"
                className="flex items-center gap-2 text-muted-foreground hover:text-foreground transition-colors duration-300"
              >
                <Mail className="w-4 h-4" />
                <EMAIL>
              </a>

              <div className="flex gap-3">
                <a
                  href="https://twitter.com/lightquant"
                  className="p-3 bg-secondary rounded-2xl hover:bg-electric-blue/20 hover:border-electric-blue/50 border border-border transition-all duration-300"
                >
                  <Twitter className="w-5 h-5" />
                </a>
                <a
                  href="https://discord.gg/lightquant"
                  className="p-3 bg-secondary rounded-2xl hover:bg-electric-blue/20 hover:border-electric-blue/50 border border-border transition-all duration-300"
                >
                  <MessageCircle className="w-5 h-5" />
                </a>
                <a
                  href="https://t.me/lightquant"
                  className="p-3 bg-secondary rounded-2xl hover:bg-electric-blue/20 hover:border-electric-blue/50 border border-border transition-all duration-300"
                >
                  <Send className="w-5 h-5" />
                </a>
              </div>
            </div>
          </div>
        </div>

        <Separator className="my-8" />

        {/* Bottom Bar */}
        <div className="flex flex-col md:flex-row justify-between items-center gap-4">
          <p className="text-muted-foreground text-sm">© 2025 LightQuant. All rights reserved.</p>
          <div className="flex gap-6 text-muted-foreground text-sm">
            <span className="hover:text-foreground transition-colors duration-300 cursor-pointer">
              🌐 Twitter
            </span>
            <span className="hover:text-foreground transition-colors duration-300 cursor-pointer">
              💬 Discord
            </span>
            <span className="hover:text-foreground transition-colors duration-300 cursor-pointer">
              📱 Telegram
            </span>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;

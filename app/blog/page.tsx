import { GradientText } from "@/components/ui/gradient-text";
import { getAllPosts } from "@/lib/blog";
import BlogListClient from "./BlogListClient";

const BlogPage = async () => {
  const posts = await getAllPosts();
  if (posts.length === 0) {
    return <p className="text-foreground">No posts found.</p>;
  }
  return (
    <>
      {/* Hero Section */}
      <section className="relative pt-32 pb-16 bg-background overflow-hidden">
        <div className="absolute inset-0 bg-gradient-lightquant opacity-60" />
        <div
          className="absolute inset-0 opacity-20"
          style={{
            backgroundImage: `
              linear-gradient(90deg, rgba(34, 139, 255, 0.1) 1px, transparent 1px),
              linear-gradient(rgba(34, 139, 255, 0.1) 1px, transparent 1px)
            `,
            backgroundSize: "50px 50px",
          }}
        />
        <div className="absolute top-1/4 right-1/4 w-96 h-96 bg-electric-blue/10 rounded-full blur-3xl animate-float" />
        <div className="absolute bottom-1/4 left-1/4 w-96 h-96 bg-electric-blue/5 rounded-full blur-3xl animate-float delay-1000" />

        <div className="relative z-10 max-w-7xl mx-auto px-6 text-center">
          <h1 className="text-4xl md:text-6xl font-space-grotesk font-bold text-foreground mb-6 animate-fade-in">
            Crypto Trading <GradientText variant="electric">Insights</GradientText>, Bot Strategies
            & AI Tips
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto animate-fade-in delay-200">
            Stay ahead of the market with in-depth articles on automated trading, AI strategies,
            platform updates, and more.
          </p>
        </div>
      </section>

      <div className="bg-background min-h-screen">
        <div className="max-w-7xl mx-auto px-6 py-16">
          <BlogListClient posts={posts} />
          {/* Bottom CTA */}
          <div className="mt-20 bg-gradient-electric rounded-3xl p-12 text-center text-white">
            <h2 className="text-3xl font-space-grotesk font-bold mb-4">
              Ready to put what you've learned into action?
            </h2>
            <p className="text-xl mb-8 opacity-90">
              Start your 7-day free trial and let AI trade for you.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button
                className="bg-white text-electric-blue px-8 py-4 rounded-2xl font-semibold hover:shadow-lg transition-all duration-300 transform hover:scale-105"
                type="button"
              >
                Try a Bot Now
              </button>
              <button
                className="border-2 border-white text-white px-8 py-4 rounded-2xl font-semibold hover:bg-white hover:text-electric-blue transition-all duration-300"
                type="button"
              >
                Browse All Bots
              </button>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default BlogPage;

import {
  ArrowRight,
  Brain,
  CheckCircle,
  Globe,
  MessageCircle,
  Rocket,
  Shield,
  Target,
  TrendingUp,
  Users,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { GlassCard } from "@/components/ui/glass-card";
import { GradientText } from "@/components/ui/gradient-text";

const AboutPage = () => {
  const teamMembers = [
    {
      name: "<PERSON>",
      role: "Founder & CEO",
      bio: "AI-driven trading expert and former Data Scientist at Techcombank, VPBank, and leading quant firms. <PERSON> defines the company's vision, shapes strategic direction, and leads the mission to make elite trading strategies accessible to everyday investors.",
      avatar: "/founder-01.jpeg",
      objectPosition: "object-top",
    },
    {
      name: "<PERSON><PERSON>",
      role: "Chief Technology Officer",
      bio: "Infrastructure specialist with over 10 years of experience across Southeast Asia. <PERSON><PERSON> oversees the design and scaling of our trading engine and backend systems, ensuring speed, robustness, and enterprise-grade performance.",
      avatar: "/founder-02.jpeg",
    },
    {
      name: "<PERSON><PERSON>",
      role: "Chief Product Officer",
      bio: "Developer of multiple successful Web3 applications and Product Specialist at LY Corporation. <PERSON>g leads product development with a strong focus on market research and user insight, translating customer needs into high-performing trading experiences.",
      avatar: "/founder-03.jpeg",
    },
    {
      name: "Robert Duong",
      role: "Head of Research & Strategy",
      bio: "PhD Candidate in Computer Science at Trinity College Dublin, National Olympiad winner, and hackathon champion. Robert leads alpha research and strategy development, designing and validating advanced quantitative models.",
      avatar: "/founder-04.png",
      objectPosition: "object-top",
    },
  ];

  const focusAreas = [
    {
      icon: Brain,
      title: "AI-Powered Strategies",
      description: "Launching new machine learning-driven trading strategies monthly",
    },
    {
      icon: Shield,
      title: "Smart Risk Controls",
      description: "Enhancing portfolio diversification and risk management tools",
    },
    {
      icon: TrendingUp,
      title: "Mobile Experience",
      description: "Launching a mobile app with real-time bot insights and controls",
    },
    {
      icon: Users,
      title: "Trading Community",
      description: "Building a community of smart, data-driven investors",
    },
  ];

  return (
    <>
      {/* Hero Section */}
      <section className="relative pt-32 pb-16 bg-background overflow-hidden">
        <div className="absolute inset-0 bg-gradient-lightquant opacity-60" />
        <div
          className="absolute inset-0 opacity-20"
          style={{
            backgroundImage: `
              linear-gradient(90deg, rgba(34, 139, 255, 0.1) 1px, transparent 1px),
              linear-gradient(rgba(34, 139, 255, 0.1) 1px, transparent 1px)
            `,
            backgroundSize: "50px 50px",
          }}
        />
        <div className="absolute top-1/4 right-1/4 w-96 h-96 bg-electric-blue/10 rounded-full blur-3xl animate-float" />
        <div className="absolute bottom-1/4 left-1/4 w-96 h-96 bg-electric-blue/5 rounded-full blur-3xl animate-float delay-1000" />

        <div className="relative z-10 max-w-7xl mx-auto px-6 text-center">
          <h1 className="text-4xl md:text-6xl font-space-grotesk font-bold text-foreground mb-6 animate-fade-in">
            About <GradientText variant="electric">LightQuant</GradientText>
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto animate-fade-in delay-200">
            AI Crypto Bot Platform - Redefining automated trading through cutting-edge AI and
            machine learning
          </p>
        </div>
      </section>

      <div className="bg-background">
        {/* Who We Are */}
        <section className="py-20">
          <div className="max-w-7xl mx-auto px-6">
            <div className="grid lg:grid-cols-2 gap-16 items-center">
              <div className="animate-fade-in">
                <div className="flex items-center gap-3 mb-6">
                  <Rocket className="w-8 h-8 text-electric-blue" />
                  <h2 className="text-4xl font-space-grotesk font-bold text-foreground">
                    Who We Are
                  </h2>
                </div>
                <div className="space-y-6 text-lg text-muted-foreground leading-relaxed">
                  <p>
                    We are a team of data scientists, engineers, and crypto enthusiasts on a mission
                    to redefine automated trading through cutting-edge AI and machine learning. At
                    the core of our platform is a belief that intelligent automation can unlock
                    superior trading performance while reducing human error and emotional bias.
                  </p>
                  <p>
                    Since day one, our goal has been to make AI trading accessible, transparent, and
                    profitable — not just for hedge funds or quant teams, but for every crypto
                    investor.
                  </p>
                </div>
              </div>

              <div className="relative animate-fade-in delay-300">
                <GlassCard className="p-8">
                  <div className="grid grid-cols-2 gap-6">
                    <div className="text-center">
                      <div className="w-16 h-16 bg-electric-blue/10 border border-electric-blue/20 rounded-2xl flex items-center justify-center mx-auto mb-4">
                        <Brain className="w-8 h-8 text-electric-blue" />
                      </div>
                      <h3 className="font-space-grotesk font-bold text-foreground mb-2">
                        AI-Driven
                      </h3>
                      <p className="text-muted-foreground text-sm">Machine learning at the core</p>
                    </div>
                    <div className="text-center">
                      <div className="w-16 h-16 bg-electric-blue/10 border border-electric-blue/20 rounded-2xl flex items-center justify-center mx-auto mb-4">
                        <Shield className="w-8 h-8 text-electric-blue" />
                      </div>
                      <h3 className="font-space-grotesk font-bold text-foreground mb-2">
                        Transparent
                      </h3>
                      <p className="text-muted-foreground text-sm">Full performance history</p>
                    </div>
                    <div className="text-center">
                      <div className="w-16 h-16 bg-electric-blue/10 border border-electric-blue/20 rounded-2xl flex items-center justify-center mx-auto mb-4">
                        <TrendingUp className="w-8 h-8 text-electric-blue" />
                      </div>
                      <h3 className="font-space-grotesk font-bold text-foreground mb-2">
                        Profitable
                      </h3>
                      <p className="text-muted-foreground text-sm">Data-driven results</p>
                    </div>
                    <div className="text-center">
                      <div className="w-16 h-16 bg-electric-blue/10 border border-electric-blue/20 rounded-2xl flex items-center justify-center mx-auto mb-4">
                        <Users className="w-8 h-8 text-electric-blue" />
                      </div>
                      <h3 className="font-space-grotesk font-bold text-foreground mb-2">
                        Accessible
                      </h3>
                      <p className="text-muted-foreground text-sm">For every investor</p>
                    </div>
                  </div>
                </GlassCard>
              </div>
            </div>
          </div>
        </section>

        {/* What We Do */}
        <section className="py-20 bg-gradient-lightquant">
          <div className="max-w-7xl mx-auto px-6">
            <div className="text-center mb-16">
              <div className="flex items-center justify-center gap-3 mb-6">
                <Brain className="w-8 h-8 text-electric-blue" />
                <h2 className="text-4xl font-space-grotesk font-bold text-foreground">
                  What We Do
                </h2>
              </div>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                Our platform allows users to follow and deploy high-performance trading bots that
                operate across major crypto exchanges like Binance, Bybit, and OKX.
              </p>
            </div>

            <div className="grid md:grid-cols-3 gap-8">
              {[
                {
                  icon: CheckCircle,
                  title: "Trained on Real Market Data",
                  description:
                    "Every bot learns from actual market conditions and historical patterns",
                },
                {
                  icon: CheckCircle,
                  title: "Backtested & Risk-Assessed",
                  description:
                    "Rigorous testing and stress-testing across different market conditions",
                },
                {
                  icon: CheckCircle,
                  title: "Continuously Improved",
                  description:
                    "Machine learning algorithms that adapt and evolve with market changes",
                },
              ].map((feature, index) => {
                const Icon = feature.icon;
                return (
                  <GlassCard
                    key={index}
                    className="p-8 hover:scale-105 transition-all duration-300 animate-scale-in group"
                    style={{ animationDelay: `${index * 150}ms` }}
                  >
                    <div className="text-center">
                      <div className="mb-6">
                        <Icon className="w-12 h-12 text-electric-blue mx-auto" />
                      </div>
                      <h3 className="text-xl font-space-grotesk font-bold text-foreground mb-3">
                        {feature.title}
                      </h3>
                      <p className="text-muted-foreground leading-relaxed">{feature.description}</p>
                    </div>
                  </GlassCard>
                );
              })}
            </div>

            <div className="text-center mt-12">
              <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                We handle the complex tech under the hood so you can focus on what matters — growing
                your portfolio.
              </p>
            </div>
          </div>
        </section>

        {/* Mission */}
        <section className="py-20 bg-gradient-electric">
          <div className="max-w-7xl mx-auto px-6 text-center text-white">
            <div className="flex items-center justify-center gap-3 mb-6">
              <Target className="w-8 h-8" />
              <h2 className="text-4xl font-space-grotesk font-bold">Our Mission</h2>
            </div>
            <p className="text-2xl mb-8 max-w-4xl mx-auto leading-relaxed">
              To empower crypto traders of all levels with AI-driven tools that maximize
              performance, minimize risk, and automate complex decision-making.
            </p>
            <p className="text-xl opacity-90 max-w-3xl mx-auto">
              We believe trading should be driven by data, not guesswork. That's why we invest
              heavily in research, infrastructure, and transparency — giving our users the edge they
              need in a highly volatile market.
            </p>
          </div>
        </section>

        {/* Leadership Team */}
        <section className="py-20">
          <div className="max-w-7xl mx-auto px-6">
            <div className="text-center mb-16">
              <div className="flex items-center justify-center gap-3 mb-6">
                <Users className="w-8 h-8 text-electric-blue" />
                <h2 className="text-4xl font-space-grotesk font-bold text-foreground">
                  Leadership Team
                </h2>
              </div>
            </div>

            <div className="grid sm:grid-cols-2 lg:grid-cols-4 gap-8">
              {teamMembers.map((member, index) => (
                <GlassCard
                  key={index}
                  className="p-6 hover:scale-105 transition-all duration-300 animate-scale-in group"
                  style={{ animationDelay: `${index * 150}ms` }}
                >
                  <div className="text-center">
                    <div className="relative inline-block mb-6">
                      <img
                        src={member.avatar}
                        alt={member.name}
                        className={`w-24 h-24 rounded-full object-cover mx-auto border-2 border-electric-blue/20 ${member.objectPosition || "object-center"}`}
                      />
                    </div>
                    <h3 className="text-xl font-space-grotesk font-bold text-foreground mb-1">
                      {member.name}
                    </h3>
                    <Badge variant="electric" className="mb-4">
                      {member.role}
                    </Badge>
                    <p className="text-muted-foreground leading-relaxed text-sm">{member.bio}</p>
                  </div>
                </GlassCard>
              ))}
            </div>
          </div>
        </section>

        {/* Where We're Going */}
        <section className="py-20 bg-gradient-lightquant">
          <div className="max-w-7xl mx-auto px-6">
            <div className="text-center mb-16">
              <div className="flex items-center justify-center gap-3 mb-6">
                <Globe className="w-8 h-8 text-electric-blue" />
                <h2 className="text-4xl font-space-grotesk font-bold text-foreground">
                  Where We're Going
                </h2>
              </div>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                We're building the future of fully autonomous trading, starting with crypto — and
                expanding to any asset class where algorithms can outperform.
              </p>
            </div>

            <div className="mb-12">
              <h3 className="text-2xl font-space-grotesk font-bold text-foreground text-center mb-8">
                In 2025 and beyond, our focus areas include:
              </h3>

              <div className="grid md:grid-cols-2 gap-8">
                {focusAreas.map((area, index) => {
                  const Icon = area.icon;
                  return (
                    <div
                      key={index}
                      className="group flex items-start gap-6 bg-secondary/50 backdrop-blur-sm border border-border rounded-2xl p-6 hover:bg-secondary/70 transition-all duration-300 animate-slide-in"
                      style={{ animationDelay: `${index * 150}ms` }}
                    >
                      <div className="w-12 h-12 bg-electric-blue/10 border border-electric-blue/20 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                        <Icon className="w-6 h-6 text-electric-blue" />
                      </div>
                      <div>
                        <h4 className="text-lg font-space-grotesk font-bold text-foreground mb-2">
                          {area.title}
                        </h4>
                        <p className="text-muted-foreground">{area.description}</p>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        </section>

        {/* Join Us CTA */}
        <section className="py-20">
          <div className="max-w-7xl mx-auto px-6 text-center">
            <div className="flex items-center justify-center gap-3 mb-6">
              <MessageCircle className="w-8 h-8 text-electric-blue" />
              <h2 className="text-4xl font-space-grotesk font-bold text-foreground">Join Us</h2>
            </div>
            <p className="text-2xl mb-12 max-w-3xl mx-auto text-muted-foreground">
              Whether you're a beginner or an experienced trader, our platform was built to help you
              trade smarter, not harder.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button variant="electric" size="lg">
                <span className="flex items-center gap-2">
                  Explore Bots
                  <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
                </span>
              </Button>

              <Button variant="default" size="lg">
                Start Free Trial
              </Button>

              <Button variant="outline" size="lg">
                Join Our Discord
              </Button>
            </div>
          </div>
        </section>
      </div>
    </>
  );
};

export default AboutPage;
